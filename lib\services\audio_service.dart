import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;

  bool get isPlaying => _isPlaying;

  Future<void> playAlarm() async {
    try {
      if (_isPlaying) {
        await stopAlarm();
      }

      // First try to play the asset file
      try {
        await _audioPlayer.play(AssetSource('sounds/alarm.mp3'));
        _isPlaying = true;
      } catch (e) {
        // If asset fails, play system notification sound
        await _playSystemSound();
      }
    } catch (e) {
      print('Error playing alarm: $e');
      // Fallback to system sound
      await _playSystemSound();
    }
  }

  Future<void> _playSystemSound() async {
    try {
      // Play system notification sound as fallback
      await SystemSound.play(SystemSoundType.alert);
      _isPlaying = true;
      
      // Stop playing after 2 seconds since system sounds are short
      Future.delayed(const Duration(seconds: 2), () {
        _isPlaying = false;
      });
    } catch (e) {
      print('Error playing system sound: $e');
    }
  }

  Future<void> stopAlarm() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
    } catch (e) {
      print('Error stopping alarm: $e');
    }
  }

  Future<void> playBeep() async {
    try {
      // Play a short beep sound
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      print('Error playing beep: $e');
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
