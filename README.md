# Bluetooth Device Monitor

A Flutter Android app that continuously monitors paired/connected Bluetooth devices and triggers alarms when monitored devices disconnect or unpair.

## Features

- **Device List Display**: Shows all paired/connected Bluetooth devices on the main page
- **Real-time Status Monitoring**: Continuously checks the connection status and paired status of each device
- **Alarm System**: Allows users to enable an alarm for any device by toggling an alarm button
- **Audio Alerts**: Plays a beep sound when a bluetooth device with enabled alarm becomes unpaired or disconnected
- **Visual Indicators**: Shows device status with color-coded indicators and status badges
- **Last Seen Tracking**: Displays when each device was last seen/connected

## UI Components

### Home Screen

- List of paired/connected devices with their names, MAC addresses, and status
- Real-time status updates showing connection and pairing status
- Monitoring indicator showing the app is actively checking device status

### Device List Items

- Device name and MAC address display
- Color-coded status indicators (Green: Connected & Paired, Blue: Paired, Red: Disconnected)
- Alarm toggle switch for each device
- Visual alarm indicator when alarm is enabled
- Last seen timestamp

### Status Indicators

- **Connected & Paired**: Green indicator
- **Paired Only**: Blue indicator
- **Disconnected**: Red indicator
- **Alarm Enabled**: Orange alarm badge

## Technical Implementation

### Architecture

- **State Management**: Provider pattern for reactive state management
- **Service Layer**: Bluetooth monitoring service with mock data for demonstration
- **Audio Service**: Handles alarm sound playback
- **Model Layer**: Bluetooth device data models

### Key Components

- `BluetoothService`: Manages device monitoring and status updates
- `AudioService`: Handles alarm sound playback
- `BluetoothDeviceModel`: Data model for device information
- `DeviceListItem`: Custom widget for displaying device information
- `HomeScreen`: Main application screen

### Real Bluetooth Implementation

This version uses **real Bluetooth functionality** with flutter_blue_plus:

- Connects to your actual paired Bluetooth devices from Android settings
- Monitors real connection and pairing status every 5 seconds
- Triggers alarms when monitored devices actually disconnect or unpair
- Requests proper Bluetooth permissions on Android (location, Bluetooth scan/connect)
- Listens to real Bluetooth adapter state changes
- Works with any Bluetooth device paired to your Android phone

## Installation

### Prerequisites

- Flutter SDK (3.6.0 or higher)
- Android SDK
- Android device or emulator

### Building the APK

1. Clone the repository
2. Navigate to the project directory
3. Get dependencies:
   ```bash
   flutter pub get
   ```
4. Build debug APK:
   ```bash
   flutter build apk --debug
   ```
5. Build release APK:
   ```bash
   flutter build apk --release
   ```

### APK Locations

- Debug APK: `build/app/outputs/flutter-apk/app-debug.apk`
- Release APK: `build/app/outputs/flutter-apk/app-release.apk` (18.8MB)

## Usage

1. Install the APK on your Android device
2. Make sure you have some Bluetooth devices paired in Android Settings → Bluetooth
3. Open the Bluetooth Device Monitor app
4. Grant Bluetooth and location permissions when prompted
5. View the list of your actual paired Bluetooth devices
6. Toggle the alarm switch for devices you want to monitor
7. When a monitored device disconnects or gets unpaired, you'll hear an alarm sound
8. The app monitors device status every 5 seconds automatically

## Permissions

The app includes the following Android permissions in the manifest:

- `BLUETOOTH` - Basic Bluetooth functionality
- `BLUETOOTH_ADMIN` - Bluetooth administration
- `ACCESS_COARSE_LOCATION` - Required for Bluetooth scanning
- `ACCESS_FINE_LOCATION` - Required for Bluetooth scanning
- `BLUETOOTH_SCAN` - For Android 12+ Bluetooth scanning
- `BLUETOOTH_CONNECT` - For Android 12+ Bluetooth connections
- `BLUETOOTH_ADVERTISE` - For Android 12+ Bluetooth advertising
- `WAKE_LOCK` - For alarm functionality

## Future Enhancements

To make this a production-ready app:

1. **Background Monitoring**: Implement background service for continuous monitoring when app is closed
2. **Notification System**: Add push notifications for device disconnections
3. **Settings Screen**: Allow users to configure monitoring intervals and alarm sounds
4. **Device History**: Track connection/disconnection history
5. **Custom Alarm Sounds**: Allow users to select custom alarm sounds
6. **Battery Optimization**: Implement efficient monitoring to preserve battery life
7. **Connection Quality**: Show signal strength and connection quality indicators
8. **Device Categories**: Group devices by type (audio, input, etc.)

## Testing

Run tests with:

```bash
flutter test
```

## Development

This project demonstrates:

- Flutter app architecture with Provider state management
- Custom UI components and widgets
- Service layer implementation
- Mock data for testing and demonstration
- Android APK building and deployment

The app now includes real Bluetooth functionality and serves as a solid foundation for production use.
