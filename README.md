# Bluetooth Device Monitor

A Flutter Android app that continuously monitors paired/connected Bluetooth devices and triggers alarms when monitored devices disconnect or unpair.

## Features

- **Device List Display**: Shows all paired/connected Bluetooth devices on the main page
- **Real-time Status Monitoring**: Continuously checks the connection status and paired status of each device
- **Alarm System**: Allows users to enable an alarm for any device by toggling an alarm button
- **Audio Alerts**: Plays a beep sound when a bluetooth device with enabled alarm becomes unpaired or disconnected
- **Visual Indicators**: Shows device status with color-coded indicators and status badges
- **Last Seen Tracking**: Displays when each device was last seen/connected

## UI Components

### Home Screen

- List of paired/connected devices with their names, MAC addresses, and status
- Real-time status updates showing connection and pairing status
- Monitoring indicator showing the app is actively checking device status

### Device List Items

- Device name and MAC address display
- Color-coded status indicators (Green: Connected & Paired, Blue: Paired, Red: Disconnected)
- Alarm toggle switch for each device
- Visual alarm indicator when alarm is enabled
- Last seen timestamp

### Status Indicators

- **Connected & Paired**: Green indicator
- **Paired Only**: Blue indicator
- **Disconnected**: Red indicator
- **Alarm Enabled**: Orange alarm badge

## Technical Implementation

### Architecture

- **State Management**: Provider pattern for reactive state management
- **Service Layer**: Bluetooth monitoring service with mock data for demonstration
- **Audio Service**: Handles alarm sound playback
- **Model Layer**: Bluetooth device data models

### Key Components

- `BluetoothService`: Manages device monitoring and status updates
- `AudioService`: Handles alarm sound playback
- `BluetoothDeviceModel`: Data model for device information
- `DeviceListItem`: Custom widget for displaying device information
- `HomeScreen`: Main application screen

### Mock Implementation

This version uses mock data to demonstrate the functionality without requiring actual Bluetooth hardware. The mock service:

- Creates sample Bluetooth devices (AirPods, Galaxy Buds, Mouse, Keyboard)
- Simulates random connection status changes (5% chance every 2 seconds)
- Triggers alarms when monitored devices "disconnect"

## Installation

### Prerequisites

- Flutter SDK (3.6.0 or higher)
- Android SDK
- Android device or emulator

### Building the APK

1. Clone the repository
2. Navigate to the project directory
3. Get dependencies:
   ```bash
   flutter pub get
   ```
4. Build debug APK:
   ```bash
   flutter build apk --debug
   ```
5. Build release APK:
   ```bash
   flutter build apk --release
   ```

### APK Locations

- Debug APK: `build/app/outputs/flutter-apk/app-debug.apk`
- Release APK: `build/app/outputs/flutter-apk/app-release.apk` (18.8MB)

## Usage

1. Install the APK on your Android device
2. Open the Bluetooth Device Monitor app
3. View the list of mock Bluetooth devices
4. Toggle the alarm switch for devices you want to monitor
5. Watch as the app simulates connection status changes
6. When a monitored device "disconnects", you'll hear an alarm sound

## Permissions

The app includes the following Android permissions in the manifest:

- `BLUETOOTH` - Basic Bluetooth functionality
- `BLUETOOTH_ADMIN` - Bluetooth administration
- `ACCESS_COARSE_LOCATION` - Required for Bluetooth scanning
- `ACCESS_FINE_LOCATION` - Required for Bluetooth scanning
- `BLUETOOTH_SCAN` - For Android 12+ Bluetooth scanning
- `BLUETOOTH_CONNECT` - For Android 12+ Bluetooth connections
- `BLUETOOTH_ADVERTISE` - For Android 12+ Bluetooth advertising
- `WAKE_LOCK` - For alarm functionality

## Future Enhancements

To make this a production-ready app with real Bluetooth functionality:

1. **Real Bluetooth Integration**: Replace mock service with actual Bluetooth APIs
2. **Background Monitoring**: Implement background service for continuous monitoring
3. **Notification System**: Add push notifications for device disconnections
4. **Settings Screen**: Allow users to configure monitoring intervals and alarm sounds
5. **Device History**: Track connection/disconnection history
6. **Custom Alarm Sounds**: Allow users to select custom alarm sounds
7. **Battery Optimization**: Implement efficient monitoring to preserve battery life

## Testing

Run tests with:

```bash
flutter test
```

## Development

This project demonstrates:

- Flutter app architecture with Provider state management
- Custom UI components and widgets
- Service layer implementation
- Mock data for testing and demonstration
- Android APK building and deployment

The app serves as a foundation that can be extended with real Bluetooth functionality for production use.
