import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/bluetooth_service.dart';
import '../widgets/device_list_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Bluetooth Device Monitor',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<BluetoothService>(
            builder: (context, bluetoothService, child) {
              return IconButton(
                icon: Icon(
                  bluetoothService.isScanning
                      ? Icons.bluetooth_searching
                      : Icons.refresh,
                ),
                onPressed: bluetoothService.isScanning
                    ? null
                    : () => bluetoothService.refresh(),
                tooltip: 'Refresh devices',
              );
            },
          ),
        ],
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          if (!bluetoothService.bluetoothEnabled) {
            return _buildBluetoothDisabledView(bluetoothService);
          }

          if (bluetoothService.devices.isEmpty) {
            return _buildEmptyView(bluetoothService);
          }

          return _buildDeviceList(bluetoothService);
        },
      ),
      floatingActionButton: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          return FloatingActionButton(
            onPressed: () => bluetoothService.refresh(),
            tooltip: 'Refresh devices',
            child: Icon(
              bluetoothService.isScanning
                  ? Icons.bluetooth_searching
                  : Icons.bluetooth_searching,
            ),
          );
        },
      ),
    );
  }

  Widget _buildBluetoothDisabledView(BluetoothService bluetoothService) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_disabled,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'Bluetooth is disabled',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              'Please enable Bluetooth to monitor your devices',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => bluetoothService.enableBluetooth(),
              icon: const Icon(Icons.bluetooth),
              label: const Text('Enable Bluetooth'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView(BluetoothService bluetoothService) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bluetooth_searching,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'No paired devices found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              'Pair some Bluetooth devices in your system settings to monitor them here',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => bluetoothService.refresh(),
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceList(BluetoothService bluetoothService) {
    return Column(
      children: [
        // Status bar
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: Row(
            children: [
              Icon(
                Icons.bluetooth_connected,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${bluetoothService.devices.length} device(s) found',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const Spacer(),
              Text(
                'Monitoring active',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(width: 4),
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
        // Device list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: bluetoothService.devices.length,
            itemBuilder: (context, index) {
              final device = bluetoothService.devices[index];
              return DeviceListItem(
                device: device,
                onAlarmToggle: () =>
                    bluetoothService.toggleAlarm(device.address),
              );
            },
          ),
        ),
      ],
    );
  }
}
