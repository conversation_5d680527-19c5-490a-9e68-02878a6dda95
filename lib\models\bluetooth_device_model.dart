class BluetoothDeviceModel {
  final String name;
  final String address;
  final bool isConnected;
  final bool isPaired;
  final bool alarmEnabled;
  final DateTime lastSeen;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    required this.isConnected,
    required this.isPaired,
    required this.alarmEnabled,
    required this.lastSeen,
  });

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    bool? isConnected,
    bool? isPaired,
    bool? alarmEnabled,
    DateTime? lastSeen,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      isConnected: isConnected ?? this.isConnected,
      isPaired: isPaired ?? this.isPaired,
      alarmEnabled: alarmEnabled ?? this.alarmEnabled,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  String get displayName => name.isNotEmpty ? name : 'Unknown Device';
  
  String get statusText {
    if (isConnected && isPaired) return 'Connected & Paired';
    if (isPaired) return 'Paired';
    if (isConnected) return 'Connected';
    return 'Disconnected';
  }

  bool get isActive => isConnected || isPaired;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluetoothDeviceModel && other.address == address;
  }

  @override
  int get hashCode => address.hashCode;

  @override
  String toString() {
    return 'BluetoothDeviceModel(name: $name, address: $address, isConnected: $isConnected, isPaired: $isPaired, alarmEnabled: $alarmEnabled)';
  }
}
