import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/bluetooth_device_model.dart';
import 'audio_service.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final AudioService _audioService = AudioService();

  List<BluetoothDeviceModel> _devices = [];
  final bool _isScanning = false;
  bool _bluetoothEnabled = false;
  Timer? _monitoringTimer;

  List<BluetoothDeviceModel> get devices => _devices;
  bool get isScanning => _isScanning;
  bool get bluetoothEnabled => _bluetoothEnabled;

  Future<void> initialize() async {
    await _requestPermissions();
    await _checkBluetoothState();
    await _loadPairedDevices();
    _startMonitoring();
  }

  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.bluetooth,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.location,
      ].request();

      debugPrint('Permission statuses: $statuses');
    }
  }

  Future<void> _checkBluetoothState() async {
    try {
      BluetoothAdapterState state = await FlutterBluePlus.adapterState.first;
      _bluetoothEnabled = state == BluetoothAdapterState.on;
      notifyListeners();
    } catch (e) {
      debugPrint('Error checking Bluetooth state: $e');
      _bluetoothEnabled = false;
    }
  }

  Future<void> _loadPairedDevices() async {
    try {
      if (!_bluetoothEnabled) return;

      // Get system bonded devices
      List<BluetoothDevice> bondedDevices = await FlutterBluePlus.bondedDevices;

      _devices = bondedDevices.map((device) {
        return BluetoothDeviceModel(
          name: device.platformName.isNotEmpty
              ? device.platformName
              : 'Unknown Device',
          address: device.remoteId.str,
          isConnected: false, // Will be updated in monitoring
          isPaired: true,
          alarmEnabled: false,
          lastSeen: DateTime.now(),
        );
      }).toList();

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  void _startMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _updateDeviceStatuses();
    });
  }

  Future<void> _updateDeviceStatuses() async {
    if (!_bluetoothEnabled) return;

    try {
      // Get current bonded devices
      List<BluetoothDevice> currentBonded = await FlutterBluePlus.bondedDevices;
      Set<String> bondedAddresses =
          currentBonded.map((d) => d.remoteId.str).toSet();

      // Update existing devices and check for disconnections
      List<BluetoothDeviceModel> updatedDevices = [];

      for (var device in _devices) {
        bool stillPaired = bondedAddresses.contains(device.address);
        bool wasConnected = device.isConnected;
        bool wasPaired = device.isPaired;

        // For simplicity, we'll consider a device "connected" if it's paired
        // In a real implementation, you'd check actual connection status
        bool isConnected = stillPaired;

        var updatedDevice = device.copyWith(
          isConnected: isConnected,
          isPaired: stillPaired,
          lastSeen: stillPaired ? DateTime.now() : device.lastSeen,
        );

        // Check if we need to trigger an alarm
        if (device.alarmEnabled) {
          bool shouldTriggerAlarm = false;

          if (wasPaired && !stillPaired) {
            shouldTriggerAlarm = true;
            debugPrint(
                'Device ${device.name} became unpaired - triggering alarm');
          } else if (wasConnected && !isConnected) {
            shouldTriggerAlarm = true;
            debugPrint('Device ${device.name} disconnected - triggering alarm');
          }

          if (shouldTriggerAlarm) {
            _audioService.playAlarm();
          }
        }

        updatedDevices.add(updatedDevice);
      }

      // Add any new bonded devices
      for (var bondedDevice in currentBonded) {
        bool exists =
            _devices.any((d) => d.address == bondedDevice.remoteId.str);
        if (!exists) {
          updatedDevices.add(BluetoothDeviceModel(
            name: bondedDevice.platformName.isNotEmpty
                ? bondedDevice.platformName
                : 'Unknown Device',
            address: bondedDevice.remoteId.str,
            isConnected: true,
            isPaired: true,
            alarmEnabled: false,
            lastSeen: DateTime.now(),
          ));
        }
      }

      _devices = updatedDevices;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating device statuses: $e');
    }
  }

  void toggleAlarm(String deviceAddress) {
    int index = _devices.indexWhere((d) => d.address == deviceAddress);
    if (index != -1) {
      _devices[index] = _devices[index].copyWith(
        alarmEnabled: !_devices[index].alarmEnabled,
      );
      notifyListeners();
    }
  }

  Future<void> enableBluetooth() async {
    try {
      await FlutterBluePlus.turnOn();
      await _checkBluetoothState();
      if (_bluetoothEnabled) {
        await _loadPairedDevices();
      }
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
    }
  }

  Future<void> refresh() async {
    await _checkBluetoothState();
    if (_bluetoothEnabled) {
      await _loadPairedDevices();
    }
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    _audioService.dispose();
    super.dispose();
  }
}
