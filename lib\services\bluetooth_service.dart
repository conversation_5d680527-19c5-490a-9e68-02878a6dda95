import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/bluetooth_device_model.dart';
import 'audio_service.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final AudioService _audioService = AudioService();

  List<BluetoothDeviceModel> _devices = [];
  bool _isScanning = false;
  bool _bluetoothEnabled = false;
  Timer? _monitoringTimer;
  StreamSubscription<BluetoothAdapterState>? _bluetoothStateSubscription;

  List<BluetoothDeviceModel> get devices => _devices;
  bool get isScanning => _isScanning;
  bool get bluetoothEnabled => _bluetoothEnabled;

  Future<void> initialize() async {
    try {
      await _requestPermissions();
      await _checkBluetoothState();
      _listenToBluetoothState();
      if (_bluetoothEnabled) {
        await _loadPairedDevices();
        _startMonitoring();
      }
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
      _bluetoothEnabled = false;
      notifyListeners();
    }
  }

  Future<void> _requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        Map<Permission, PermissionStatus> statuses = await [
          Permission.bluetooth,
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.location,
        ].request();

        debugPrint('Permission statuses: $statuses');
      }
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
    }
  }

  void _listenToBluetoothState() {
    try {
      _bluetoothStateSubscription =
          FlutterBluePlus.adapterState.listen((BluetoothAdapterState state) {
        _bluetoothEnabled = state == BluetoothAdapterState.on;
        debugPrint('Bluetooth state changed: $state');

        if (_bluetoothEnabled) {
          _loadPairedDevices();
          _startMonitoring();
        } else {
          _devices.clear();
          _stopMonitoring();
        }
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Error listening to Bluetooth state: $e');
    }
  }

  Future<void> _checkBluetoothState() async {
    try {
      BluetoothAdapterState state = await FlutterBluePlus.adapterState.first;
      _bluetoothEnabled = state == BluetoothAdapterState.on;
      notifyListeners();
    } catch (e) {
      debugPrint('Error checking Bluetooth state: $e');
      _bluetoothEnabled = false;
      notifyListeners();
    }
  }

  Future<void> _loadPairedDevices() async {
    try {
      if (!_bluetoothEnabled) return;

      List<BluetoothDevice> bondedDevices = await FlutterBluePlus.bondedDevices;

      List<BluetoothDeviceModel> newDevices = [];

      for (BluetoothDevice device in bondedDevices) {
        // Check if device already exists to preserve alarm settings
        BluetoothDeviceModel? existingDevice =
            _devices.where((d) => d.address == device.remoteId.str).isNotEmpty
                ? _devices.firstWhere((d) => d.address == device.remoteId.str)
                : null;

        // Check connection status by attempting to connect (non-blocking)
        bool isConnected = await _checkDeviceConnection(device);

        newDevices.add(BluetoothDeviceModel(
          name: device.platformName.isNotEmpty
              ? device.platformName
              : 'Unknown Device',
          address: device.remoteId.str,
          isConnected: isConnected,
          isPaired: true,
          alarmEnabled: existingDevice?.alarmEnabled ?? false,
          lastSeen: isConnected
              ? DateTime.now()
              : (existingDevice?.lastSeen ?? DateTime.now()),
        ));
      }

      _devices = newDevices;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  Future<bool> _checkDeviceConnection(BluetoothDevice device) async {
    try {
      // Try to get connected devices
      // Note: flutter_bluetooth_serial has limitations in checking active connections
      // This is a simplified approach - real connection checking would require
      // platform-specific code or different Bluetooth libraries

      // For now, we'll use a heuristic: if device is bonded, assume it might be connected
      // In a production app, you'd want to implement more sophisticated connection detection
      return false; // Conservative approach - assume not connected initially
    } catch (e) {
      debugPrint('Error checking device connection: $e');
      return false;
    }
  }

  void _startMonitoring() {
    _stopMonitoring();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _updateDeviceStatuses();
    });
  }

  void _stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  Future<void> _updateDeviceStatuses() async {
    if (!_bluetoothEnabled) return;

    try {
      // Get current bonded devices
      List<BluetoothDevice> currentBondedDevices =
          await FlutterBluePlus.bondedDevices;
      Set<String> currentAddresses =
          currentBondedDevices.map((d) => d.remoteId.str).toSet();

      List<BluetoothDeviceModel> updatedDevices = [];

      for (var device in _devices) {
        bool wasConnected = device.isConnected;
        bool wasPaired = device.isPaired;

        // Check if device is still paired
        bool stillPaired = currentAddresses.contains(device.address);

        // For connection status, we'll use a simple heuristic
        // Since flutter_bluetooth_serial has limitations in real-time connection detection,
        // we'll track connection based on pairing status and time
        bool isConnected = stillPaired &&
            (device.lastSeen
                .isAfter(DateTime.now().subtract(const Duration(minutes: 5))));

        var updatedDevice = device.copyWith(
          isConnected: isConnected,
          isPaired: stillPaired,
          lastSeen: stillPaired ? DateTime.now() : device.lastSeen,
        );

        // Check if we need to trigger an alarm
        if (device.alarmEnabled) {
          bool shouldTriggerAlarm = false;

          if (wasPaired && !stillPaired) {
            shouldTriggerAlarm = true;
            debugPrint(
                'Device ${device.name} became unpaired - triggering alarm');
          } else if (wasConnected && !isConnected) {
            shouldTriggerAlarm = true;
            debugPrint('Device ${device.name} disconnected - triggering alarm');
          }

          if (shouldTriggerAlarm) {
            _audioService.playAlarm();
          }
        }

        updatedDevices.add(updatedDevice);
      }

      // Add any newly paired devices
      for (BluetoothDevice bondedDevice in currentBondedDevices) {
        bool exists =
            _devices.any((d) => d.address == bondedDevice.remoteId.str);
        if (!exists) {
          bool isConnected = await _checkDeviceConnection(bondedDevice);
          updatedDevices.add(BluetoothDeviceModel(
            name: bondedDevice.platformName.isNotEmpty
                ? bondedDevice.platformName
                : 'Unknown Device',
            address: bondedDevice.remoteId.str,
            isConnected: isConnected,
            isPaired: true,
            alarmEnabled: false,
            lastSeen: DateTime.now(),
          ));
        }
      }

      _devices = updatedDevices;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating device statuses: $e');
    }
  }

  void toggleAlarm(String deviceAddress) {
    int index = _devices.indexWhere((d) => d.address == deviceAddress);
    if (index != -1) {
      _devices[index] = _devices[index].copyWith(
        alarmEnabled: !_devices[index].alarmEnabled,
      );
      notifyListeners();
    }
  }

  Future<void> enableBluetooth() async {
    try {
      await FlutterBluePlus.turnOn();
      await _checkBluetoothState();
      if (_bluetoothEnabled) {
        await _loadPairedDevices();
        _startMonitoring();
      }
    } catch (e) {
      debugPrint('Error enabling Bluetooth: $e');
    }
  }

  Future<void> refresh() async {
    await _checkBluetoothState();
    if (_bluetoothEnabled) {
      await _loadPairedDevices();
    }
  }

  @override
  void dispose() {
    _stopMonitoring();
    _bluetoothStateSubscription?.cancel();
    _audioService.dispose();
    super.dispose();
  }
}
