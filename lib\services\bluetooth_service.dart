import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/bluetooth_device_model.dart';
import 'audio_service.dart';

class BluetoothService extends ChangeNotifier {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final AudioService _audioService = AudioService();

  List<BluetoothDeviceModel> _devices = [];
  final bool _isScanning = false;
  bool _bluetoothEnabled = true; // Mock as enabled
  Timer? _monitoringTimer;
  final Random _random = Random();

  List<BluetoothDeviceModel> get devices => _devices;
  bool get isScanning => _isScanning;
  bool get bluetoothEnabled => _bluetoothEnabled;

  Future<void> initialize() async {
    await _loadMockDevices();
    _startMonitoring();
  }

  Future<void> _loadMockDevices() async {
    // Create mock Bluetooth devices for demonstration
    _devices = [
      BluetoothDeviceModel(
        name: 'AirPods Pro',
        address: '00:1A:2B:3C:4D:5E',
        isConnected: true,
        isPaired: true,
        alarmEnabled: false,
        lastSeen: DateTime.now(),
      ),
      BluetoothDeviceModel(
        name: 'Samsung Galaxy Buds',
        address: '00:1A:2B:3C:4D:5F',
        isConnected: false,
        isPaired: true,
        alarmEnabled: false,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      BluetoothDeviceModel(
        name: 'Bluetooth Mouse',
        address: '00:1A:2B:3C:4D:60',
        isConnected: true,
        isPaired: true,
        alarmEnabled: false,
        lastSeen: DateTime.now(),
      ),
      BluetoothDeviceModel(
        name: 'Wireless Keyboard',
        address: '00:1A:2B:3C:4D:61',
        isConnected: false,
        isPaired: true,
        alarmEnabled: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];

    notifyListeners();
  }

  void _startMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _updateDeviceStatuses();
    });
  }

  Future<void> _updateDeviceStatuses() async {
    if (!_bluetoothEnabled) return;

    // Mock device status changes for demonstration
    List<BluetoothDeviceModel> updatedDevices = [];

    for (var device in _devices) {
      bool wasConnected = device.isConnected;
      bool wasPaired = device.isPaired;

      // Randomly simulate connection changes (5% chance)
      bool isConnected = device.isConnected;
      if (_random.nextInt(100) < 5) {
        // 5% chance of status change
        isConnected = !isConnected;
      }

      var updatedDevice = device.copyWith(
        isConnected: isConnected,
        lastSeen: isConnected ? DateTime.now() : device.lastSeen,
      );

      // Check if we need to trigger an alarm
      if (device.alarmEnabled) {
        bool shouldTriggerAlarm = false;

        if (wasPaired && !updatedDevice.isPaired) {
          shouldTriggerAlarm = true;
          debugPrint(
              'Device ${device.name} became unpaired - triggering alarm');
        } else if (wasConnected && !isConnected) {
          shouldTriggerAlarm = true;
          debugPrint('Device ${device.name} disconnected - triggering alarm');
        }

        if (shouldTriggerAlarm) {
          _audioService.playAlarm();
        }
      }

      updatedDevices.add(updatedDevice);
    }

    _devices = updatedDevices;
    notifyListeners();
  }

  void toggleAlarm(String deviceAddress) {
    int index = _devices.indexWhere((d) => d.address == deviceAddress);
    if (index != -1) {
      _devices[index] = _devices[index].copyWith(
        alarmEnabled: !_devices[index].alarmEnabled,
      );
      notifyListeners();
    }
  }

  Future<void> enableBluetooth() async {
    // Mock enabling Bluetooth
    _bluetoothEnabled = true;
    notifyListeners();
  }

  Future<void> refresh() async {
    // Mock refresh - reload mock devices
    await _loadMockDevices();
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    _audioService.dispose();
    super.dispose();
  }
}
